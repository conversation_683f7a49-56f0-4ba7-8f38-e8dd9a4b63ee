"""
Sort a Python List

@author: <PERSON><PERSON><PERSON><PERSON> and <PERSON>

# TODO <PERSON>, 13210602

"""

from typing import List
from dataclasses import dataclass


# dataclasses are a convenient new way to create simple "structs" or "records"
# in Python.
@dataclass
class Product:
    name: str
    price: float


# hint: lst is a python list, so use lst[i] to access element i
# (=what we called "get" in class), and use len(lst) to get its size.


def sort_insertion(lst: List[Product]) -> List[Product]:
    """Sort lst by insertion, in-place."""
    # TODO
    for i in range(1, len(lst)):
        product = lst[i]
        j = i - 1
        while j >= 0 and lst[j].price > product.price:
            lst[j + 1] = lst[j]
            j -= 1
        lst[j + 1] = product
    return lst


def merge(lst_a: List[Product], lst_b: List[Product]) -> List[Product]:
    """Given sorted lists lst_a and lst_b, combine them into a new sorted list.
    """
    # TODO
    sorted_list = []
    i = j = 0
    while i < len(lst_a) and j < len(lst_b):
        if lst_a[i].price <= lst_b[j].price:
            sorted_list.append(lst_a[i])
            i += 1
        else:
            sorted_list.append(lst_b[j])
            j += 1

    sorted_list.extend(lst_a[i:])
    sorted_list.extend(lst_b[j:])
    return sorted_list


def sort_merge(lst: List[Product]) -> List[Product]:
    """Sort lst by merge sort. Leave the input list untouched.

    Since merge sort is not in-place, leave the input list unmodified.
    """
    # TODO
    if len(lst) <= 1:
        return lst
    mid = len(lst) // 2
    left = sort_merge(lst[:mid])
    right = sort_merge(lst[mid:])
    return merge(left, right)


def _partition_lomuto(lst: List[Product], p: int, r: int) -> int:
    """Lomuto partitioning of sublist lst[p:r] (including p, excluding r).
    Use the last element as pivot.

    Return the index of the pivot after permutations.
    """
    # TODO
    pivot = lst[r - 1]
    i = p - 1
    for j in range(p, r - 1):
        if lst[j].price < pivot.price:
            lst[i], lst[j] = lst[j], lst[i]
            i += 1
    lst[i+1], lst[r-1] = lst[r-1], lst[i+1]
    return i + 1


def _partition_hoare(lst: List[Product], p: int, r: int) -> int:
    """Hoare partitioning of sublist lst[p:r] (including p, excluding r).
    Use the middle element as pivot. (if even length, take the "floor").

    Return the index of the pivot after permutations.
    """
    # TODO
    pass


def _quicksort_recurse_lomuto(lst: List[Product], p: int, r: int) -> None:
    """Quicksort recursion: sort lst[p:r] (including p, excluding r).

    Use the Lomuto partitioning strategy and recursion, as in class:
    recurse into lst[p:q] and lst[q+1:r].

    Do not return anything, change lst in place.

    (internal function)
    """
    # TODO
    if p < r - 1:
        q = _partition_lomuto(lst, p, r)
        _quicksort_recurse_lomuto(lst, p, q)
        _quicksort_recurse_lomuto(lst, q + 1, r)


def _quicksort_recurse_hoare(lst: List[Product], p: int, r: int) -> None:
    """Quicksort recursion: sort lst[p:r] (including p, excluding r).

    Use the Hoare partitioning strategy and recursion:
    recurse into lst[p:q+1] and lst[q+1:r].

    Do not return anything, change lst in place.

    (internal function)
    """
    # TODO
    pass


def sort_quick_lomuto(lst: List[Product]) -> List[Product]:
    """Sort lst by in-place quick sort using the Lomuto scheme."""
    # TODO
    _quicksort_recurse_lomuto(lst, 0, len(lst))
    return lst


def sort_quick_hoare(lst: List[Product]) -> List[Product]:
    """Sort lst by in-place quick sort using the Hoare scheme."""
    # TODO
    pass


def main():
    sample_inventory = [
        Product(name="banana", price=5.99),
        Product(name="peanut butter", price=3),
        Product(name="jelly", price=3),
        Product(name="Little Oblivions CD", price=12),
        Product(name="guitar strings", price=4.20),
    ]

    print("original list")
    print(sample_inventory)

    # Python built-in sort: use lambda-expression to select the sort key.
    print("built-in sort")
    print(sorted(sample_inventory, key=lambda x: x.price))

    # insertion sort
    print("insertion sort")
    # since insertion sort is in place, we first make a copy of the inventory,
    # so that we can keep testing other sorting algos afterward.
    sample_inv_copy = sample_inventory.copy()
    print(sort_insertion(sample_inv_copy))

    # merge sort
    print("merge sort")
    print(sort_merge(sample_inventory))

    # quick sort
    print("quick sort")
    # since quicksort is in place, we make a copy.
    sample_inv_copy = sample_inventory.copy()
    print(sort_quick_lomuto(sample_inv_copy))

    # quick sort alternative partitioning
    print("quick sort (Hoare)")
    # since quicksort is in place, we make a copy.
    sample_inv_copy = sample_inventory.copy()
    print(sort_quick_hoare(sample_inv_copy))


if __name__ == "__main__":
    main()
