"""
Dictionary demo
@author: <PERSON><PERSON><PERSON><PERSON> and <PERSON> and <PERSON>

# TODO Add your name and student number here.
"""

from __future__ import annotations


class Word:
    def __init__(self, key: str, value, link):
        self.key = key
        self.value = value
        self.link = link


class Bucket:
    def __init__(self):
        """Initialize an empty bucket."""
        # TODO

    def lookup(self, key: str):
        """Find the correct key and return the word. If the key cannot be
        found raise a KeyError."""
        # TODO

    def insert(self, key: str, value):
        """Insert a word into the bucket. If the key doesn't exists create a
        new word. If the key already exists only update its value."""
        # TODO

    def delete(self, key: str):
        """Delete the given key from the bucket or raise a KeyError if the
        word cannot be found."""
        # TODO

    def __len__(self):
        """Return the number of words in the bucket."""
        # TODO


class Dictionary:
    def __init__(self, n_buckets: int = 20):
        self.n_buckets = n_buckets
        self._ht = [Bucket() for _ in range(n_buckets)]

    def bucket_sizes(self):
        """List of bucket sizes"""
        return [len(bucket) for bucket in self._ht]

    def load_factor(self):
        """Return the load factor alpha of the dictionary"""
        # TODO

    def _hash_first(self, key: str):
        """Hash the given string to a number between [0, n_buckets) using the
        character value of the first character"""
        if not isinstance(key, str):
            raise ValueError("Our dictionary only supports string keys.")
        return (ord(key[0]) if key else 0) % self.n_buckets

    def _hash_length(self, key: str):
        """Hash the given string to a number between [0, n_buckets) using the
        length of the word"""
        if not isinstance(key, str):
            raise ValueError("Our dictionary only supports string keys.")
        # TODO

    def _hash_sum(self, key: str):
        """Hash the given string to a number between [0, n_buckets) using the
        sum of the character values in the word"""
        if not isinstance(key, str):
            raise ValueError("Our dictionary only supports string keys.")
        # TODO

    # change this to select a different hash function
    _hash = _hash_first

    def update(self, key: str, value):
        """Set the value associated with the given key."""
        # TODO

    def lookup(self, key: str):
        """Return the value associated with the given key.
        This method should raise a KeyError if the key is not found."""
        # TODO

    def get(self, key, defaultvalue=None):
        """Return the value associated with the given key, or the default value
        if the key is not found. This method *should not* raise a KeyError if the key is not found.
        """
        # TODO

    def items(self):
        """Return a list of tuples containing the key-value pairs from the dictionary."""
        # TODO

    def delete(self, key: str):
        """Remove key from dictionary."""
        # TODO

    def invert(self) -> Dictionary:
        """Return a new dictionary where all key-value pairs are inverted,
        such that the old values are the new keys, and viceversa.
        Important: since the inverted dictionary can be one-to-many,
        each value in inverted dictionary must be a *list* of elements in any order.
        Hint: it is convenient to use `items` and `get` methods for this task.
        """
        # TODO

    # the following definitions permit our custom dictionary
    # to mimic a builtin python dictionary.

    def __getitem__(self, key: str):
        return self.lookup(key)

    def __setitem__(self, key: str, value):
        return self.update(key, value)

    def __delitem__(self, key: str):
        return self.delete(key)

    def __len__(self):
        return sum([len(bucket) for bucket in self._ht])
